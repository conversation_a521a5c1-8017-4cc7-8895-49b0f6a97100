package processor

import (
	"context"
	"fmt"
	"log"
	"sync"

	"alarm_distribution/internal/config"
	"alarm_distribution/internal/models"
	"alarm_distribution/internal/notifications"
	"alarm_distribution/internal/storage"
)

// AlertProcessor handles the processing of alert groups
type AlertProcessor struct {
	storage   *storage.MongoStorage
	channels  map[string]models.NotificationChannel
	config    *config.Config
	formatter *notifications.MessageFormatter
}

// NewAlertProcessor creates a new alert processor
func NewAlertProcessor(storage *storage.MongoStorage, channels map[string]models.NotificationChannel, cfg *config.Config) *AlertProcessor {
	return &AlertProcessor{
		storage:   storage,
		channels:  channels,
		config:    cfg,
		formatter: &notifications.MessageFormatter{},
	}
}

// ProcessAlertGroup processes a group of alerts
func (p *AlertProcessor) ProcessAlertGroup(ctx context.Context, alertGroup *models.AlertGroup) error {
	log.Printf("Processing alert group with %d alerts", len(alertGroup.Alerts))

	// Process each alert in the group
	for _, alert := range alertGroup.Alerts {
		if err := p.ProcessAlert(ctx, &alert); err != nil {
			log.Printf("Failed to process alert %s: %v", alert.ID, err)
			// Continue processing other alerts even if one fails
			continue
		}
	}

	return nil
}

// ProcessAlert processes a single alert
func (p *AlertProcessor) ProcessAlert(ctx context.Context, alert *models.Alert) error {
	log.Printf("Processing alert: %s (fingerprint: %s, status: %s)", 
		alert.AlertName, alert.Fingerprint, alert.Status)

	// Check if this is a duplicate or update to existing alert
	existingAlert, err := p.storage.GetAlert(ctx, alert.Fingerprint, alert.ProjectName)
	if err != nil {
		return fmt.Errorf("failed to check existing alert: %w", err)
	}

	// Determine if we should send notification
	shouldNotify := p.shouldSendNotification(alert, existingAlert)

	// Save alert to storage
	if err := p.storage.SaveAlert(ctx, alert); err != nil {
		return fmt.Errorf("failed to save alert: %w", err)
	}

	// Send notifications if needed
	if shouldNotify {
		if err := p.sendNotifications(ctx, alert); err != nil {
			log.Printf("Failed to send notifications for alert %s: %v", alert.ID, err)
			// Don't return error here as the alert was saved successfully
		}
	}

	return nil
}

// shouldSendNotification determines if a notification should be sent
func (p *AlertProcessor) shouldSendNotification(newAlert *models.Alert, existingAlert *models.Alert) bool {
	// Always send notification for new alerts
	if existingAlert == nil {
		return true
	}

	// Send notification if status changed
	if newAlert.Status != existingAlert.Status {
		return true
	}

	// Send notification if severity changed
	if newAlert.Severity != existingAlert.Severity {
		return true
	}

	// Don't send notification for duplicate alerts with same status and severity
	return false
}

// sendNotifications sends notifications through configured channels
func (p *AlertProcessor) sendNotifications(ctx context.Context, alert *models.Alert) error {
	// Get project configuration
	projectConfig, exists := p.config.Projects[alert.ProjectName]
	if !exists {
		return fmt.Errorf("project configuration not found: %s", alert.ProjectName)
	}

	// Find notification rules for this alert's severity
	var channelsToNotify []string
	for _, rule := range projectConfig.Rules {
		if rule.Severity == string(alert.Severity) {
			channelsToNotify = rule.Channels
			break
		}
	}

	// Fallback to default channels if no specific rule found
	if len(channelsToNotify) == 0 {
		channelsToNotify = projectConfig.Channels
	}

	if len(channelsToNotify) == 0 {
		log.Printf("No notification channels configured for alert: %s", alert.AlertName)
		return nil
	}

	// Format notification message
	message := p.formatter.FormatAlertMessage(alert)

	// Send notifications concurrently
	var wg sync.WaitGroup
	errChan := make(chan error, len(channelsToNotify))

	for _, channelName := range channelsToNotify {
		channel, exists := p.channels[channelName]
		if !exists {
			log.Printf("Notification channel not found: %s", channelName)
			continue
		}

		wg.Add(1)
		go func(ch models.NotificationChannel, chName string) {
			defer wg.Done()
			
			if err := ch.Send(message); err != nil {
				log.Printf("Failed to send notification via %s: %v", chName, err)
				errChan <- fmt.Errorf("channel %s: %w", chName, err)
			} else {
				log.Printf("Successfully sent notification via %s for alert: %s", chName, alert.AlertName)
			}
		}(channel, channelName)
	}

	// Wait for all notifications to complete
	wg.Wait()
	close(errChan)

	// Collect any errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("notification errors: %v", errors)
	}

	return nil
}
