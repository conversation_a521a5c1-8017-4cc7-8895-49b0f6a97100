package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config represents the main configuration structure
type Config struct {
	Server       ServerConfig              `yaml:"server"`
	MongoDB      MongoDBConfig             `yaml:"mongodb"`
	Projects     map[string]ProjectConfig  `yaml:"projects"`
	Notifications NotificationConfig       `yaml:"notifications"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// MongoDBConfig contains MongoDB connection configuration
type MongoDBConfig struct {
	URI        string `yaml:"uri"`
	Database   string `yaml:"database"`
	Collection string `yaml:"collection"`
}

// ProjectConfig contains configuration for each alert project
type ProjectConfig struct {
	Name        string                    `yaml:"name"`
	Type        string                    `yaml:"type"` // alertmanager, aliyun_arms, etc.
	Endpoint    string                    `yaml:"endpoint"`
	Channels    []string                  `yaml:"channels"`
	SeverityMap map[string]string         `yaml:"severity_map"`
	Rules       []NotificationRule        `yaml:"rules"`
}

// NotificationRule defines when and how to send notifications
type NotificationRule struct {
	Severity string   `yaml:"severity"`
	Channels []string `yaml:"channels"`
}

// NotificationConfig contains all notification channel configurations
type NotificationConfig struct {
	Feishu FeishuConfig `yaml:"feishu"`
	// Add other notification channels here
}

// FeishuConfig contains Feishu notification configuration
type FeishuConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
	Enabled   bool   `yaml:"enabled"`
}

// Load loads configuration from YAML file
func Load(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Set defaults
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.MongoDB.Database == "" {
		config.MongoDB.Database = "alarm_distribution"
	}
	if config.MongoDB.Collection == "" {
		config.MongoDB.Collection = "alerts"
	}

	return &config, nil
}
