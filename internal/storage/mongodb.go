package storage

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"alarm_distribution/internal/config"
	"alarm_distribution/internal/models"
)

// MongoStorage implements alert storage using MongoDB
type MongoStorage struct {
	client     *mongo.Client
	database   *mongo.Database
	collection *mongo.Collection
}

// NewMongoStorage creates a new MongoDB storage instance
func NewMongoStorage(cfg *config.MongoDBConfig) (*MongoStorage, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(cfg.URI))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Test the connection
	if err := client.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	database := client.Database(cfg.Database)
	collection := database.Collection(cfg.Collection)

	storage := &MongoStorage{
		client:     client,
		database:   database,
		collection: collection,
	}

	// Create indexes
	if err := storage.createIndexes(ctx); err != nil {
		return nil, fmt.Errorf("failed to create indexes: %w", err)
	}

	return storage, nil
}

// createIndexes creates necessary indexes for efficient querying
func (s *MongoStorage) createIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "fingerprint", Value: 1},
				{Key: "project_name", Value: 1},
			},
			Options: options.Index().SetUnique(false),
		},
		{
			Keys: bson.D{
				{Key: "status", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "severity", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "created_at", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "starts_at", Value: -1},
			},
		},
	}

	_, err := s.collection.Indexes().CreateMany(ctx, indexes)
	return err
}

// SaveAlert saves an alert to MongoDB
func (s *MongoStorage) SaveAlert(ctx context.Context, alert *models.Alert) error {
	alert.UpdatedAt = time.Now()

	// Use upsert to handle duplicate fingerprints
	filter := bson.M{
		"fingerprint":  alert.Fingerprint,
		"project_name": alert.ProjectName,
	}

	update := bson.M{
		"$set": alert,
		"$setOnInsert": bson.M{
			"created_at": alert.CreatedAt,
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := s.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save alert: %w", err)
	}

	return nil
}

// SaveAlertGroup saves a group of alerts to MongoDB
func (s *MongoStorage) SaveAlertGroup(ctx context.Context, alertGroup *models.AlertGroup) error {
	for _, alert := range alertGroup.Alerts {
		if err := s.SaveAlert(ctx, &alert); err != nil {
			return fmt.Errorf("failed to save alert %s: %w", alert.ID, err)
		}
	}
	return nil
}

// GetAlert retrieves an alert by fingerprint and project name
func (s *MongoStorage) GetAlert(ctx context.Context, fingerprint, projectName string) (*models.Alert, error) {
	filter := bson.M{
		"fingerprint":  fingerprint,
		"project_name": projectName,
	}

	var alert models.Alert
	err := s.collection.FindOne(ctx, filter).Decode(&alert)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get alert: %w", err)
	}

	return &alert, nil
}

// GetAlertsByStatus retrieves alerts by status
func (s *MongoStorage) GetAlertsByStatus(ctx context.Context, status models.AlertStatus, limit int) ([]models.Alert, error) {
	filter := bson.M{"status": status}
	opts := options.Find().SetLimit(int64(limit)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := s.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find alerts: %w", err)
	}
	defer cursor.Close(ctx)

	var alerts []models.Alert
	if err := cursor.All(ctx, &alerts); err != nil {
		return nil, fmt.Errorf("failed to decode alerts: %w", err)
	}

	return alerts, nil
}

// GetAlertsBySeverity retrieves alerts by severity
func (s *MongoStorage) GetAlertsBySeverity(ctx context.Context, severity models.AlertSeverity, limit int) ([]models.Alert, error) {
	filter := bson.M{"severity": severity}
	opts := options.Find().SetLimit(int64(limit)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := s.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find alerts: %w", err)
	}
	defer cursor.Close(ctx)

	var alerts []models.Alert
	if err := cursor.All(ctx, &alerts); err != nil {
		return nil, fmt.Errorf("failed to decode alerts: %w", err)
	}

	return alerts, nil
}

// UpdateAlertStatus updates the status of an alert
func (s *MongoStorage) UpdateAlertStatus(ctx context.Context, fingerprint, projectName string, status models.AlertStatus, endsAt *time.Time) error {
	filter := bson.M{
		"fingerprint":  fingerprint,
		"project_name": projectName,
	}

	update := bson.M{
		"$set": bson.M{
			"status":     status,
			"updated_at": time.Now(),
		},
	}

	if endsAt != nil {
		update["$set"].(bson.M)["ends_at"] = *endsAt
	}

	_, err := s.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update alert status: %w", err)
	}

	return nil
}

// Close closes the MongoDB connection
func (s *MongoStorage) Close(ctx context.Context) error {
	return s.client.Disconnect(ctx)
}
