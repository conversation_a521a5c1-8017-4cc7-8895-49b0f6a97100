server:
  host: "0.0.0.0"
  port: 8080

mongodb:
  uri: "mongodb://localhost:27017"
  database: "alarm_distribution"
  collection: "alerts"

projects:
  prometheus:
    name: "Prometheus AlertManager"
    type: "alertmanager"
    endpoint: "/webhook/prometheus"
    channels: ["feishu"]
    severity_map:
      critical: "ERROR"
      warning: "WARN"
      info: "INFO"
    rules:
      - severity: "ERROR"
        channels: ["feishu"]
      - severity: "WARN"
        channels: ["feishu"]
      - severity: "INFO"
        channels: ["feishu"]

  aliyun_arms:
    name: "Aliyun ARMS"
    type: "aliyun_arms"
    endpoint: "/webhook/aliyun_arms"
    channels: ["feishu"]
    severity_map:
      ERROR: "ERROR"
      WARN: "WARN"
      INFO: "INFO"
    rules:
      - severity: "ERROR"
        channels: ["feishu"]
      - severity: "WARN"
        channels: ["feishu"]

notifications:
  feishu:
    app_id: "YOUR_APP_ID"
    app_secret: "YOUR_APP_SECRET"
    enabled: true
